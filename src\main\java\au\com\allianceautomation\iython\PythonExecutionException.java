package au.com.allianceautomation.iython;

/**
 * Exception thrown when Python code execution fails.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonExecutionException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructs a new PythonExecutionException with the specified detail message.
     * 
     * @param message the detail message
     */
    public PythonExecutionException(String message) {
        super(message);
    }
    
    /**
     * Constructs a new PythonExecutionException with the specified detail message and cause.
     * 
     * @param message the detail message
     * @param cause the cause
     */
    public PythonExecutionException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * Constructs a new PythonExecutionException with the specified cause.
     * 
     * @param cause the cause
     */
    public PythonExecutionException(Throwable cause) {
        super(cause);
    }
}
