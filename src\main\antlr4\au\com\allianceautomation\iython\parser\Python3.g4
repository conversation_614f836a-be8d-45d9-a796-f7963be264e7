/*
 * Python 3.8+ Grammar for ANTLR4
 * Simplified grammar focusing on core Python features
 * Supports expressions, statements, functions, classes, and modern syntax
 */

grammar Python3;

// Parser Rules

// Top-level constructs
file_input: (NEWLINE | stmt)* EOF;

// Statements
stmt: simple_stmt | compound_stmt;

simple_stmt: small_stmt (SEMI_COLON small_stmt)* SEMI_COLON? NEWLINE;

small_stmt: expr_stmt | pass_stmt | del_stmt | return_stmt | import_stmt | global_stmt | assert_stmt;

expr_stmt: expr (ASSIGN expr | augassign expr)*;

augassign: ADD_ASSIGN | SUB_ASSIGN | MULT_ASSIGN | DIV_ASSIGN | MOD_ASSIGN | POWER_ASSIGN | IDIV_ASSIGN;

pass_stmt: PASS;

del_stmt: DEL expr;

return_stmt: RETURN expr?;

import_stmt: IMPORT dotted_name (AS NAME)?;

global_stmt: GLOBAL NAME (COMMA NAME)*;

assert_stmt: ASSERT expr (COMMA expr)?;

compound_stmt: if_stmt | while_stmt | for_stmt | funcdef | classdef;

if_stmt: IF expr COLON suite (ELIF expr COLON suite)* (ELSE COLON suite)?;

while_stmt: WHILE expr COLON suite (ELSE COLON suite)?;

for_stmt: FOR NAME IN expr COLON suite (ELSE COLON suite)?;

funcdef: DEF NAME OPEN_PAREN parameters? CLOSE_PAREN COLON suite;

parameters: NAME (COMMA NAME)*;

classdef: CLASS NAME (OPEN_PAREN expr CLOSE_PAREN)? COLON suite;

suite: simple_stmt | NEWLINE INDENT stmt+ DEDENT;

// Expressions
expr: or_expr;

or_expr: and_expr (OR and_expr)*;

and_expr: not_expr (AND not_expr)*;

not_expr: NOT not_expr | comparison;

comparison: arith_expr (comp_op arith_expr)*;

comp_op: LESS_THAN | GREATER_THAN | EQUALS | GT_EQ | LT_EQ | NOT_EQ_2 | IN | NOT IN | IS | IS NOT;

arith_expr: term ((ADD | MINUS) term)*;

term: factor ((STAR | DIV | MOD | IDIV) factor)*;

factor: (ADD | MINUS | NOT_OP) factor | power;

power: atom_expr (POWER factor)?;

atom_expr: atom trailer*;

atom: NAME | NUMBER | STRING | TRUE | FALSE | NONE |
      OPEN_PAREN expr CLOSE_PAREN |
      OPEN_BRACK expr_list? CLOSE_BRACK |
      OPEN_BRACE dict_list? CLOSE_BRACE;

trailer: OPEN_PAREN arglist? CLOSE_PAREN | OPEN_BRACK expr CLOSE_BRACK | DOT NAME;

arglist: expr (COMMA expr)*;

expr_list: expr (COMMA expr)*;

dict_list: dict_item (COMMA dict_item)*;

dict_item: expr COLON expr;

dotted_name: NAME (DOT NAME)*;

// Lexer Rules

// Keywords (Python 3.8+ keywords)
FALSE: 'False';
NONE: 'None';
TRUE: 'True';
AND: 'and';
AS: 'as';
ASSERT: 'assert';
ASYNC: 'async';
AWAIT: 'await';
BREAK: 'break';
CLASS: 'class';
CONTINUE: 'continue';
DEF: 'def';
DEL: 'del';
ELIF: 'elif';
ELSE: 'else';
EXCEPT: 'except';
FINALLY: 'finally';
FOR: 'for';
FROM: 'from';
GLOBAL: 'global';
IF: 'if';
IMPORT: 'import';
IN: 'in';
IS: 'is';
LAMBDA: 'lambda';
NONLOCAL: 'nonlocal';
NOT: 'not';
OR: 'or';
PASS: 'pass';
RAISE: 'raise';
RETURN: 'return';
TRY: 'try';
WHILE: 'while';
WITH: 'with';
YIELD: 'yield';

// Operators and Delimiters
DOT: '.';
ELLIPSIS: '...';
STAR: '*';
OPEN_PAREN: '(';
CLOSE_PAREN: ')';
COMMA: ',';
COLON: ':';
SEMI_COLON: ';';
POWER: '**';
ASSIGN: '=';
OPEN_BRACK: '[';
CLOSE_BRACK: ']';
OR_OP: '|';
XOR: '^';
AND_OP: '&';
LEFT_SHIFT: '<<';
RIGHT_SHIFT: '>>';
ADD: '+';
MINUS: '-';
DIV: '/';
MOD: '%';
IDIV: '//';
NOT_OP: '~';
OPEN_BRACE: '{';
CLOSE_BRACE: '}';
LESS_THAN: '<';
GREATER_THAN: '>';
EQUALS: '==';
GT_EQ: '>=';
LT_EQ: '<=';
NOT_EQ_1: '<>';
NOT_EQ_2: '!=';
AT: '@';
ARROW: '->';
ADD_ASSIGN: '+=';
SUB_ASSIGN: '-=';
MULT_ASSIGN: '*=';
AT_ASSIGN: '@=';
DIV_ASSIGN: '/=';
MOD_ASSIGN: '%=';
AND_ASSIGN: '&=';
OR_ASSIGN: '|=';
XOR_ASSIGN: '^=';
LEFT_SHIFT_ASSIGN: '<<=';
RIGHT_SHIFT_ASSIGN: '>>=';
POWER_ASSIGN: '**=';
IDIV_ASSIGN: '//=';
WALRUS: ':=';  // Walrus operator (Python 3.8+)

// Literals
STRING: STRING_LITERAL;
NUMBER: INTEGER | FLOAT_NUMBER | IMAG_NUMBER;
INTEGER: DECIMAL_INTEGER | OCT_INTEGER | HEX_INTEGER | BIN_INTEGER;

// String literals (including f-strings)
fragment STRING_LITERAL: 
    ([rR]? [fF] | [fF] [rR]?)? (SHORT_STRING | LONG_STRING) |
    [rR]? (SHORT_STRING | LONG_STRING);

fragment SHORT_STRING: '\'' SHORT_STRING_ITEM_SINGLE* '\'' | '"' SHORT_STRING_ITEM_DOUBLE* '"';
fragment LONG_STRING: '\'\'\'' LONG_STRING_ITEM* '\'\'\'' | '"""' LONG_STRING_ITEM* '"""';

fragment SHORT_STRING_ITEM_SINGLE: SHORT_STRING_CHAR_SINGLE | STRING_ESCAPE_SEQ;
fragment SHORT_STRING_ITEM_DOUBLE: SHORT_STRING_CHAR_DOUBLE | STRING_ESCAPE_SEQ;
fragment LONG_STRING_ITEM: LONG_STRING_CHAR | STRING_ESCAPE_SEQ;

fragment SHORT_STRING_CHAR_SINGLE: ~[\\\r\n'];
fragment SHORT_STRING_CHAR_DOUBLE: ~[\\\r\n"];
fragment LONG_STRING_CHAR: ~[\\];

fragment STRING_ESCAPE_SEQ: '\\' .;

// Numeric literals
fragment DECIMAL_INTEGER: NON_ZERO_DIGIT DIGIT* | '0'+;
fragment OCT_INTEGER: '0' [oO] OCT_DIGIT+;
fragment HEX_INTEGER: '0' [xX] HEX_DIGIT+;
fragment BIN_INTEGER: '0' [bB] BIN_DIGIT+;

fragment NON_ZERO_DIGIT: [1-9];
fragment DIGIT: [0-9];
fragment OCT_DIGIT: [0-7];
fragment HEX_DIGIT: [0-9a-fA-F];
fragment BIN_DIGIT: [01];

fragment FLOAT_NUMBER: POINT_FLOAT | EXPONENT_FLOAT;
fragment POINT_FLOAT: INT_PART? FRACTION | INT_PART '.';
fragment EXPONENT_FLOAT: (INT_PART | POINT_FLOAT) EXPONENT;
fragment INT_PART: DIGIT+;
fragment FRACTION: '.' DIGIT+;
fragment EXPONENT: [eE] [+-]? DIGIT+;

fragment IMAG_NUMBER: (FLOAT_NUMBER | INT_PART) [jJ];

// Identifiers
NAME: ID_START ID_CONTINUE*;

fragment ID_START: [a-zA-Z_];
fragment ID_CONTINUE: [a-zA-Z0-9_];

// Whitespace and comments
NEWLINE: ('\r'? '\n' | '\r') SPACES?;
WS: [ \t]+ -> skip;
COMMENT: '#' ~[\r\n]* -> skip;

// Indentation tokens (handled by custom logic)
INDENT: 'INDENT_TOKEN';
DEDENT: 'DEDENT_TOKEN';

// Special tokens
SPACES: [ \t]+;

// Error handling
ErrorChar: .;
