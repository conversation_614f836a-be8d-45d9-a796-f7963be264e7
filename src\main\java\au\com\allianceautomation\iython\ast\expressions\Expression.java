package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTNode;

/**
 * Base class for all expression nodes in the Python AST.
 * Expressions are nodes that evaluate to a value.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public abstract class Expression extends ASTNode {
    
    public Expression(int line, int column) {
        super(line, column);
    }
}
