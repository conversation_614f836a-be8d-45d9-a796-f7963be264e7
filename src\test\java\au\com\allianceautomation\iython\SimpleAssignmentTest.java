package au.com.allianceautomation.iython;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SimpleAssignmentTest {
    
    private PythonExecutor executor;
    
    @BeforeEach
    void setUp() {
        executor = new PythonExecutor();
    }
    
    @Test
    void testSimpleIntegerAssignment() throws PythonExecutionException {
        String code = "x = 42";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(x)", "x");
        assertEquals(42L, result); // Numbers are parsed as Long
    }
    
    @Test
    void testSimpleStringAssignment() throws PythonExecutionException {
        String code = "y = \"Hello\"";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(y)", "y");
        assertEquals("Hello", result);
    }

    @Test
    void testSimpleListAssignment() throws PythonExecutionException {
        String code = "z = [1, 2, 3]";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(z)", "z");
        assertTrue(result instanceof java.util.List);
        java.util.List<?> list = (java.util.List<?>) result;
        assertEquals(3, list.size());
        assertEquals(1L, list.get(0));
        assertEquals(2L, list.get(1));
        assertEquals(3L, list.get(2));
    }
}
