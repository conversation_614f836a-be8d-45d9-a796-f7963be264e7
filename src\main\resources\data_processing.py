#!/usr/bin/env python
"""
Data processing example for iython.
Demonstrates working with data structures and algorithms.
"""

def process_data():
    """Process sample data and return results."""

    # Sample data
    data = [
        {"name": "<PERSON>", "age": 30, "department": "Engineering"},
        {"name": "<PERSON>", "age": 25, "department": "Marketing"},
        {"name": "<PERSON>", "age": 35, "department": "Engineering"},
        {"name": "<PERSON>", "age": 28, "department": "Sales"},
        {"name": "<PERSON>", "age": 32, "department": "Engineering"}
    ]

    print("Original data:")
    for person in data:
        print("  " + str(person))

    # Filter engineers
    engineers = [person for person in data if person["department"] == "Engineering"]
    print("\nEngineers (" + str(len(engineers)) + "):")
    for engineer in engineers:
        print("  " + engineer['name'] + ", age " + str(engineer['age']))

    # Calculate average age
    total_age = sum(person["age"] for person in data)
    average_age = float(total_age) / len(data)
    print("\nAverage age: " + str(round(average_age, 1)))

    # Group by department
    departments = {}
    for person in data:
        dept = person["department"]
        if dept not in departments:
            departments[dept] = []
        departments[dept].append(person)

    print("\nGrouped by department:")
    for dept, people in departments.items():
        print("  " + dept + ": " + str(len(people)) + " people")
        for person in people:
            print("    - " + person['name'])

    return {
        "total_people": len(data),
        "engineers_count": len(engineers),
        "average_age": average_age,
        "departments": list(departments.keys())
    }

def main():
    print("Data Processing Example")
    print("=" * 50)

    result = process_data()

    print("\nSummary:")
    print("Total people: " + str(result['total_people']))
    print("Engineers: " + str(result['engineers_count']))
    print("Average age: " + str(round(result['average_age'], 1)))
    print("Departments: " + ', '.join(result['departments']))

if __name__ == "__main__":
    main()
