package au.com.allianceautomation.iython.ast;

import au.com.allianceautomation.iython.ast.expressions.*;
import au.com.allianceautomation.iython.ast.statements.*;

/**
 * Visitor interface for traversing and processing AST nodes.
 * Implements the visitor pattern for type-safe AST traversal.
 * 
 * @param <T> The return type of visitor methods
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public interface ASTVisitor<T> {
    
    // Expression visitors
    T visitBinaryOp(BinaryOpExpression node);
    T visitUnaryOp(UnaryOpExpression node);
    T visitLiteral(LiteralExpression node);
    T visitName(NameExpression node);
    T visitCall(CallExpression node);
    T visitAttribute(AttributeExpression node);
    T visitSubscript(SubscriptExpression node);
    T visitList(ListExpression node);
    T visitDict(DictExpression node);
    T visitCompare(CompareExpression node);
    T visitBoolOp(BoolOpExpression node);
    
    // Statement visitors
    T visitExpressionStmt(ExpressionStatement node);
    T visitAssignment(AssignmentStatement node);
    T visitIf(IfStatement node);
    T visitWhile(WhileStatement node);
    T visitFor(ForStatement node);
    T visitFunctionDef(FunctionDefStatement node);
    T visitClassDef(ClassDefStatement node);
    T visitReturn(ReturnStatement node);
    T visitPass(PassStatement node);
    T visitDelete(DeleteStatement node);
    T visitImport(ImportStatement node);
    T visitGlobal(GlobalStatement node);
    T visitAssert(AssertStatement node);
    
    // Program visitor
    T visitProgram(Program node);
}
