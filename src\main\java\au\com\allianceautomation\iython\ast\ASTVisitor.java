package au.com.allianceautomation.iython.ast;

import au.com.allianceautomation.iython.ast.expressions.AttributeExpression;
import au.com.allianceautomation.iython.ast.expressions.BinaryOpExpression;
import au.com.allianceautomation.iython.ast.expressions.BoolOpExpression;
import au.com.allianceautomation.iython.ast.expressions.CallExpression;
import au.com.allianceautomation.iython.ast.expressions.CompareExpression;
import au.com.allianceautomation.iython.ast.expressions.ConditionalExpression;
import au.com.allianceautomation.iython.ast.expressions.DictExpression;
import au.com.allianceautomation.iython.ast.expressions.ListExpression;
import au.com.allianceautomation.iython.ast.expressions.LiteralExpression;
import au.com.allianceautomation.iython.ast.expressions.NameExpression;
import au.com.allianceautomation.iython.ast.expressions.SubscriptExpression;
import au.com.allianceautomation.iython.ast.expressions.UnaryOpExpression;
import au.com.allianceautomation.iython.ast.statements.AssertStatement;
import au.com.allianceautomation.iython.ast.statements.AssignmentStatement;
import au.com.allianceautomation.iython.ast.statements.ClassDefStatement;
import au.com.allianceautomation.iython.ast.statements.DeleteStatement;
import au.com.allianceautomation.iython.ast.statements.ExpressionStatement;
import au.com.allianceautomation.iython.ast.statements.ForStatement;
import au.com.allianceautomation.iython.ast.statements.FunctionDefStatement;
import au.com.allianceautomation.iython.ast.statements.GlobalStatement;
import au.com.allianceautomation.iython.ast.statements.IfStatement;
import au.com.allianceautomation.iython.ast.statements.ImportStatement;
import au.com.allianceautomation.iython.ast.statements.PassStatement;
import au.com.allianceautomation.iython.ast.statements.ReturnStatement;
import au.com.allianceautomation.iython.ast.statements.WhileStatement;

/**
 * Visitor interface for traversing and processing AST nodes.
 * Implements the visitor pattern for type-safe AST traversal.
 * 
 * @param <T> The return type of visitor methods
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public interface ASTVisitor<T> {
    
    // Expression visitors
    T visitBinaryOp(BinaryOpExpression node);
    T visitUnaryOp(UnaryOpExpression node);
    T visitLiteral(LiteralExpression node);
    T visitName(NameExpression node);
    T visitCall(CallExpression node);
    T visitAttribute(AttributeExpression node);
    T visitSubscript(SubscriptExpression node);
    T visitList(ListExpression node);
    T visitDict(DictExpression node);
    T visitCompare(CompareExpression node);
    T visitBoolOp(BoolOpExpression node);
    T visitConditional(ConditionalExpression node);
    
    // Statement visitors
    T visitExpressionStmt(ExpressionStatement node);
    T visitAssignment(AssignmentStatement node);
    T visitIf(IfStatement node);
    T visitWhile(WhileStatement node);
    T visitFor(ForStatement node);
    T visitFunctionDef(FunctionDefStatement node);
    T visitClassDef(ClassDefStatement node);
    T visitReturn(ReturnStatement node);
    T visitPass(PassStatement node);
    T visitDelete(DeleteStatement node);
    T visitImport(ImportStatement node);
    T visitGlobal(GlobalStatement node);
    T visitAssert(AssertStatement node);
    
    // Program visitor
    T visitProgram(Program node);
}
