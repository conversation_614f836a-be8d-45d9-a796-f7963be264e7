package au.com.allianceautomation.iython.parser;

import java.util.ArrayList;
import java.util.List;

import org.antlr.v4.runtime.tree.ParseTree;

import au.com.allianceautomation.iython.ast.ASTNode;
import au.com.allianceautomation.iython.ast.Program;
import au.com.allianceautomation.iython.ast.expressions.AttributeExpression;
import au.com.allianceautomation.iython.ast.expressions.BinaryOpExpression;
import au.com.allianceautomation.iython.ast.expressions.BoolOpExpression;
import au.com.allianceautomation.iython.ast.expressions.CallExpression;
import au.com.allianceautomation.iython.ast.expressions.CompareExpression;
import au.com.allianceautomation.iython.ast.expressions.ConditionalExpression;
import au.com.allianceautomation.iython.ast.expressions.Expression;
import au.com.allianceautomation.iython.ast.expressions.ListExpression;
import au.com.allianceautomation.iython.ast.expressions.LiteralExpression;
import au.com.allianceautomation.iython.ast.expressions.NameExpression;
import au.com.allianceautomation.iython.ast.expressions.SubscriptExpression;
import au.com.allianceautomation.iython.ast.expressions.UnaryOpExpression;
import au.com.allianceautomation.iython.ast.statements.AssignmentStatement;
import au.com.allianceautomation.iython.ast.statements.ExpressionStatement;
import au.com.allianceautomation.iython.ast.statements.PassStatement;
import au.com.allianceautomation.iython.ast.statements.Statement;

/**
 * Converts ANTLR parse trees to our custom AST representation.
 * Extends the generated Python3BaseVisitor to traverse the parse tree.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonASTBuilder extends Python3BaseVisitor<ASTNode> {
    
    @Override
    public Program visitFile_input(Python3Parser.File_inputContext ctx) {
        List<Statement> statements = new ArrayList<>();
        
        for (Python3Parser.StmtContext stmtCtx : ctx.stmt()) {
            Statement stmt = (Statement) visit(stmtCtx);
            if (stmt != null) {
                statements.add(stmt);
            }
        }
        
        return new Program(statements);
    }
    
    @Override
    public Statement visitStmt(Python3Parser.StmtContext ctx) {
        if (ctx.simple_stmt() != null) {
            return (Statement) visit(ctx.simple_stmt());
        } else if (ctx.compound_stmt() != null) {
            return (Statement) visit(ctx.compound_stmt());
        }
        return null;
    }
    
    @Override
    public Statement visitSimple_stmt(Python3Parser.Simple_stmtContext ctx) {
        // For now, just handle the first small statement
        if (!ctx.small_stmt().isEmpty()) {
            return (Statement) visit(ctx.small_stmt(0));
        }
        return null;
    }
    
    @Override
    public Statement visitSmall_stmt(Python3Parser.Small_stmtContext ctx) {
        if (ctx.expr_stmt() != null) {
            return (Statement) visit(ctx.expr_stmt());
        } else if (ctx.pass_stmt() != null) {
            return new PassStatement(getLine(ctx), getColumn(ctx));
        } else if (ctx.return_stmt() != null) {
            return (Statement) visit(ctx.return_stmt());
        }
        // Handle other statement types as needed
        return null;
    }
    
    @Override
    public Statement visitExpr_stmt(Python3Parser.Expr_stmtContext ctx) {
        Expression expr = (Expression) visit(ctx.expr(0));

        // Check if this is an assignment (has ASSIGN token)
        if (ctx.ASSIGN() != null && !ctx.ASSIGN().isEmpty() && ctx.expr().size() > 1) {
            // This is an assignment: target = value
            Expression target = expr;
            Expression value = (Expression) visit(ctx.expr(1));
            return new AssignmentStatement(getLine(ctx), getColumn(ctx), target, value);
        } else if (ctx.augassign() != null && !ctx.augassign().isEmpty() && ctx.expr().size() > 1) {
            // This is an augmented assignment (+=, -=, etc.)
            // For now, treat as regular assignment
            Expression target = expr;
            Expression value = (Expression) visit(ctx.expr(1));
            return new AssignmentStatement(getLine(ctx), getColumn(ctx), target, value);
        } else {
            // This is just an expression statement
            return new ExpressionStatement(getLine(ctx), getColumn(ctx), expr);
        }
    }
    
    @Override
    public Expression visitExpr(Python3Parser.ExprContext ctx) {
        if (ctx.conditional_expr() != null) {
            return (Expression) visit(ctx.conditional_expr());
        } else if (ctx.lambda_expr() != null) {
            return (Expression) visit(ctx.lambda_expr());
        }
        return null;
    }

    @Override
    public Expression visitConditional_expr(Python3Parser.Conditional_exprContext ctx) {
        if (ctx.IF() != null) {
            // Ternary expression: test if condition else orelse
            Expression test = (Expression) visit(ctx.or_expr(1));
            Expression body = (Expression) visit(ctx.or_expr(0));
            Expression orelse = (Expression) visit(ctx.conditional_expr());
            return new ConditionalExpression(getLine(ctx), getColumn(ctx), test, body, orelse);
        } else {
            return (Expression) visit(ctx.or_expr(0));
        }
    }

    @Override
    public Expression visitLambda_expr(Python3Parser.Lambda_exprContext ctx) {
        // For now, return a placeholder - lambda expressions need more complex handling
        return new NameExpression(getLine(ctx), getColumn(ctx), "lambda");
    }
    
    @Override
    public Expression visitOr_expr(Python3Parser.Or_exprContext ctx) {
        if (ctx.and_expr().size() == 1) {
            return (Expression) visit(ctx.and_expr(0));
        } else {
            // Handle OR operations
            List<Expression> values = new ArrayList<>();
            for (Python3Parser.And_exprContext andCtx : ctx.and_expr()) {
                values.add((Expression) visit(andCtx));
            }
            return new BoolOpExpression(getLine(ctx), getColumn(ctx), 
                                      BoolOpExpression.BoolOperator.OR, values);
        }
    }
    
    @Override
    public Expression visitAnd_expr(Python3Parser.And_exprContext ctx) {
        if (ctx.not_expr().size() == 1) {
            return (Expression) visit(ctx.not_expr(0));
        } else {
            // Handle AND operations
            List<Expression> values = new ArrayList<>();
            for (Python3Parser.Not_exprContext notCtx : ctx.not_expr()) {
                values.add((Expression) visit(notCtx));
            }
            return new BoolOpExpression(getLine(ctx), getColumn(ctx), 
                                      BoolOpExpression.BoolOperator.AND, values);
        }
    }
    
    @Override
    public Expression visitNot_expr(Python3Parser.Not_exprContext ctx) {
        if (ctx.NOT() != null) {
            Expression operand = (Expression) visit(ctx.not_expr());
            return new UnaryOpExpression(getLine(ctx), getColumn(ctx), 
                                       UnaryOpExpression.UnaryOperator.NOT, operand);
        } else {
            return (Expression) visit(ctx.comparison());
        }
    }
    
    @Override
    public Expression visitComparison(Python3Parser.ComparisonContext ctx) {
        if (ctx.comp_op().isEmpty()) {
            return (Expression) visit(ctx.bitwise_or(0));
        } else {
            // Handle comparison operations
            Expression left = (Expression) visit(ctx.bitwise_or(0));

            if (ctx.comp_op().size() == 1) {
                // Simple binary comparison
                BinaryOpExpression.BinaryOperator op = convertCompOp(ctx.comp_op(0));
                Expression right = (Expression) visit(ctx.bitwise_or(1));
                return new BinaryOpExpression(getLine(ctx), getColumn(ctx), left, op, right);
            } else {
                // Multiple comparisons - convert to CompareExpression
                List<BinaryOpExpression.BinaryOperator> ops = new ArrayList<>();
                List<Expression> comparators = new ArrayList<>();

                for (int i = 0; i < ctx.comp_op().size(); i++) {
                    ops.add(convertCompOp(ctx.comp_op(i)));
                    comparators.add((Expression) visit(ctx.bitwise_or(i + 1)));
                }

                return new CompareExpression(getLine(ctx), getColumn(ctx), left, ops, comparators);
            }
        }
    }

    @Override
    public Expression visitBitwise_or(Python3Parser.Bitwise_orContext ctx) {
        if (ctx.bitwise_xor().size() == 1) {
            return (Expression) visit(ctx.bitwise_xor(0));
        } else {
            // Handle bitwise OR operations
            Expression left = (Expression) visit(ctx.bitwise_xor(0));

            for (int i = 1; i < ctx.bitwise_xor().size(); i++) {
                Expression right = (Expression) visit(ctx.bitwise_xor(i));
                left = new BinaryOpExpression(getLine(ctx), getColumn(ctx), left,
                                            BinaryOpExpression.BinaryOperator.BIT_OR, right);
            }

            return left;
        }
    }

    @Override
    public Expression visitBitwise_xor(Python3Parser.Bitwise_xorContext ctx) {
        if (ctx.bitwise_and().size() == 1) {
            return (Expression) visit(ctx.bitwise_and(0));
        } else {
            // Handle bitwise XOR operations
            Expression left = (Expression) visit(ctx.bitwise_and(0));

            for (int i = 1; i < ctx.bitwise_and().size(); i++) {
                Expression right = (Expression) visit(ctx.bitwise_and(i));
                left = new BinaryOpExpression(getLine(ctx), getColumn(ctx), left,
                                            BinaryOpExpression.BinaryOperator.BIT_XOR, right);
            }

            return left;
        }
    }

    @Override
    public Expression visitBitwise_and(Python3Parser.Bitwise_andContext ctx) {
        if (ctx.shift_expr().size() == 1) {
            return (Expression) visit(ctx.shift_expr(0));
        } else {
            // Handle bitwise AND operations
            Expression left = (Expression) visit(ctx.shift_expr(0));

            for (int i = 1; i < ctx.shift_expr().size(); i++) {
                Expression right = (Expression) visit(ctx.shift_expr(i));
                left = new BinaryOpExpression(getLine(ctx), getColumn(ctx), left,
                                            BinaryOpExpression.BinaryOperator.BIT_AND, right);
            }

            return left;
        }
    }

    @Override
    public Expression visitShift_expr(Python3Parser.Shift_exprContext ctx) {
        if (ctx.arith_expr().size() == 1) {
            return (Expression) visit(ctx.arith_expr(0));
        } else {
            // Handle shift operations
            Expression left = (Expression) visit(ctx.arith_expr(0));

            for (int i = 1; i < ctx.arith_expr().size(); i++) {
                BinaryOpExpression.BinaryOperator op;
                if (ctx.LEFT_SHIFT(i-1) != null) {
                    op = BinaryOpExpression.BinaryOperator.LEFT_SHIFT;
                } else {
                    op = BinaryOpExpression.BinaryOperator.RIGHT_SHIFT;
                }

                Expression right = (Expression) visit(ctx.arith_expr(i));
                left = new BinaryOpExpression(getLine(ctx), getColumn(ctx), left, op, right);
            }

            return left;
        }
    }
    
    @Override
    public Expression visitArith_expr(Python3Parser.Arith_exprContext ctx) {
        if (ctx.term().size() == 1) {
            return (Expression) visit(ctx.term(0));
        } else {
            // Handle arithmetic operations
            Expression left = (Expression) visit(ctx.term(0));
            
            for (int i = 1; i < ctx.term().size(); i++) {
                BinaryOpExpression.BinaryOperator op;
                if (ctx.ADD(i-1) != null) {
                    op = BinaryOpExpression.BinaryOperator.ADD;
                } else {
                    op = BinaryOpExpression.BinaryOperator.SUBTRACT;
                }
                
                Expression right = (Expression) visit(ctx.term(i));
                left = new BinaryOpExpression(getLine(ctx), getColumn(ctx), left, op, right);
            }
            
            return left;
        }
    }
    
    @Override
    public Expression visitTerm(Python3Parser.TermContext ctx) {
        if (ctx.factor().size() == 1) {
            return (Expression) visit(ctx.factor(0));
        } else {
            // Handle term operations (*, /, %, //)
            Expression left = (Expression) visit(ctx.factor(0));
            
            for (int i = 1; i < ctx.factor().size(); i++) {
                BinaryOpExpression.BinaryOperator op;
                if (ctx.STAR(i-1) != null) {
                    op = BinaryOpExpression.BinaryOperator.MULTIPLY;
                } else if (ctx.DIV(i-1) != null) {
                    op = BinaryOpExpression.BinaryOperator.DIVIDE;
                } else if (ctx.MOD(i-1) != null) {
                    op = BinaryOpExpression.BinaryOperator.MODULO;
                } else {
                    op = BinaryOpExpression.BinaryOperator.FLOOR_DIVIDE;
                }
                
                Expression right = (Expression) visit(ctx.factor(i));
                left = new BinaryOpExpression(getLine(ctx), getColumn(ctx), left, op, right);
            }
            
            return left;
        }
    }
    
    @Override
    public Expression visitFactor(Python3Parser.FactorContext ctx) {
        if (ctx.ADD() != null || ctx.MINUS() != null || ctx.NOT_OP() != null) {
            UnaryOpExpression.UnaryOperator op;
            if (ctx.ADD() != null) {
                op = UnaryOpExpression.UnaryOperator.PLUS;
            } else if (ctx.MINUS() != null) {
                op = UnaryOpExpression.UnaryOperator.MINUS;
            } else {
                op = UnaryOpExpression.UnaryOperator.INVERT;
            }
            
            Expression operand = (Expression) visit(ctx.factor());
            return new UnaryOpExpression(getLine(ctx), getColumn(ctx), op, operand);
        } else {
            return (Expression) visit(ctx.power());
        }
    }
    
    @Override
    public Expression visitPower(Python3Parser.PowerContext ctx) {
        Expression base = (Expression) visit(ctx.atom_expr());

        if (ctx.factor() != null) {
            Expression exponent = (Expression) visit(ctx.factor());
            return new BinaryOpExpression(getLine(ctx), getColumn(ctx), base,
                                        BinaryOpExpression.BinaryOperator.POWER, exponent);
        }

        return base;
    }

    @Override
    public Expression visitAtom_expr(Python3Parser.Atom_exprContext ctx) {
        Expression base = (Expression) visit(ctx.atom());

        // Handle trailers (function calls, attribute access, subscripts)
        for (Python3Parser.TrailerContext trailerCtx : ctx.trailer()) {
            base = visitTrailerWithBase(trailerCtx, base);
        }

        return base;
    }

    private Expression visitTrailerWithBase(Python3Parser.TrailerContext ctx, Expression base) {
        if (ctx.arglist() != null) {
            // Function call
            List<Expression> arguments = new ArrayList<>();
            for (Python3Parser.ArgumentContext argCtx : ctx.arglist().argument()) {
                arguments.add((Expression) visit(argCtx));
            }
            return new CallExpression(getLine(ctx), getColumn(ctx), base, arguments);
        } else if (ctx.subscriptlist() != null) {
            // Subscript
            Expression index = (Expression) visit(ctx.subscriptlist());
            return new SubscriptExpression(getLine(ctx), getColumn(ctx), base, index);
        } else if (ctx.NAME() != null) {
            // Attribute access
            String attr = ctx.NAME().getText();
            return new AttributeExpression(getLine(ctx), getColumn(ctx), base, attr);
        }

        return base;
    }

    @Override
    public Expression visitArgument(Python3Parser.ArgumentContext ctx) {
        // For now, just handle simple expressions
        if (ctx.expr() != null && ctx.expr().size() > 0) {
            return (Expression) visit(ctx.expr(0));
        }
        return null;
    }

    @Override
    public Expression visitSubscriptlist(Python3Parser.SubscriptlistContext ctx) {
        // For now, just handle the first subscript
        if (ctx.subscript() != null && ctx.subscript().size() > 0) {
            return (Expression) visit(ctx.subscript(0));
        }
        return null;
    }

    @Override
    public Expression visitSubscript(Python3Parser.SubscriptContext ctx) {
        // For now, just handle simple expressions
        if (ctx.expr() != null && ctx.expr().size() > 0) {
            return (Expression) visit(ctx.expr(0));
        }
        return null;
    }
    
    @Override
    public Expression visitAtom(Python3Parser.AtomContext ctx) {
        if (ctx.NAME() != null) {
            return new NameExpression(getLine(ctx), getColumn(ctx), ctx.NAME().getText());
        } else if (ctx.NUMBER() != null) {
            return parseNumber(ctx.NUMBER().getText(), getLine(ctx), getColumn(ctx));
        } else if (ctx.STRING() != null) {
            return new LiteralExpression(getLine(ctx), getColumn(ctx),
                                       parseString(ctx.STRING().getText()),
                                       LiteralExpression.LiteralType.STRING);
        } else if (ctx.TRUE() != null) {
            return new LiteralExpression(getLine(ctx), getColumn(ctx), true,
                                       LiteralExpression.LiteralType.BOOLEAN);
        } else if (ctx.FALSE() != null) {
            return new LiteralExpression(getLine(ctx), getColumn(ctx), false,
                                       LiteralExpression.LiteralType.BOOLEAN);
        } else if (ctx.NONE() != null) {
            return new LiteralExpression(getLine(ctx), getColumn(ctx), null,
                                       LiteralExpression.LiteralType.NONE);
        } else if (ctx.ELLIPSIS() != null) {
            return new LiteralExpression(getLine(ctx), getColumn(ctx), "...",
                                       LiteralExpression.LiteralType.STRING);
        } else if (ctx.testlist_comp() != null) {
            // Handle parenthesized expressions, lists, etc.
            return (Expression) visit(ctx.testlist_comp());
        } else if (ctx.dictorsetmaker() != null) {
            // Handle dictionary/set literals
            return visitDictOrSetMaker(ctx.dictorsetmaker());
        } else if (ctx.yield_expr() != null) {
            // Handle yield expressions
            return (Expression) visit(ctx.yield_expr());
        }

        return null;
    }

    @Override
    public Expression visitTestlist_comp(Python3Parser.Testlist_compContext ctx) {
        // For now, handle as a simple expression list
        if (ctx.expr() != null && ctx.expr().size() == 1 && ctx.star_expr() == null) {
            // Single expression in parentheses
            return (Expression) visit(ctx.expr(0));
        } else {
            // Multiple expressions - create a list
            List<Expression> elements = new ArrayList<>();
            if (ctx.expr() != null) {
                for (Python3Parser.ExprContext exprCtx : ctx.expr()) {
                    elements.add((Expression) visit(exprCtx));
                }
            }
            if (ctx.star_expr() != null) {
                for (Python3Parser.Star_exprContext starCtx : ctx.star_expr()) {
                    elements.add((Expression) visit(starCtx));
                }
            }
            return new ListExpression(getLine(ctx), getColumn(ctx), elements);
        }
    }

    @Override
    public Expression visitStar_expr(Python3Parser.Star_exprContext ctx) {
        // For now, just return the inner expression
        return (Expression) visit(ctx.expr());
    }

    private Expression visitDictOrSetMaker(Python3Parser.DictorsetmakerContext ctx) {
        // For now, return a placeholder - dictionary/set literals need more complex handling
        return new LiteralExpression(getLine(ctx), getColumn(ctx), "{}",
                                   LiteralExpression.LiteralType.STRING);
    }

    @Override
    public Expression visitYield_expr(Python3Parser.Yield_exprContext ctx) {
        // For now, return a placeholder - yield expressions need more complex handling
        return new NameExpression(getLine(ctx), getColumn(ctx), "yield");
    }
    
    // Helper methods
    private int getLine(ParseTree ctx) {
        if (ctx instanceof Python3Parser.File_inputContext) {
            return ((Python3Parser.File_inputContext) ctx).getStart().getLine();
        }
        // Add more specific cases as needed
        return 1;
    }
    
    private int getColumn(ParseTree ctx) {
        if (ctx instanceof Python3Parser.File_inputContext) {
            return ((Python3Parser.File_inputContext) ctx).getStart().getCharPositionInLine();
        }
        // Add more specific cases as needed
        return 1;
    }
    
    private BinaryOpExpression.BinaryOperator convertCompOp(Python3Parser.Comp_opContext ctx) {
        if (ctx.LESS_THAN() != null) return BinaryOpExpression.BinaryOperator.LESS_THAN;
        if (ctx.GREATER_THAN() != null) return BinaryOpExpression.BinaryOperator.GREATER_THAN;
        if (ctx.EQUALS() != null) return BinaryOpExpression.BinaryOperator.EQUAL;
        if (ctx.GT_EQ() != null) return BinaryOpExpression.BinaryOperator.GREATER_EQUAL;
        if (ctx.LT_EQ() != null) return BinaryOpExpression.BinaryOperator.LESS_EQUAL;
        if (ctx.NOT_EQ_2() != null) return BinaryOpExpression.BinaryOperator.NOT_EQUAL;
        if (ctx.IN() != null) return BinaryOpExpression.BinaryOperator.IN;
        if (ctx.IS() != null) return BinaryOpExpression.BinaryOperator.IS;
        return BinaryOpExpression.BinaryOperator.EQUAL; // default
    }
    
    private Expression parseNumber(String text, int line, int column) {
        try {
            if (text.contains(".")) {
                return new LiteralExpression(line, column, Double.parseDouble(text), 
                                           LiteralExpression.LiteralType.FLOAT);
            } else {
                return new LiteralExpression(line, column, Long.parseLong(text), 
                                           LiteralExpression.LiteralType.INTEGER);
            }
        } catch (NumberFormatException e) {
            return new LiteralExpression(line, column, 0, LiteralExpression.LiteralType.INTEGER);
        }
    }
    
    private String parseString(String text) {
        // Remove quotes and handle escape sequences
        if (text.length() >= 2) {
            return text.substring(1, text.length() - 1);
        }
        return text;
    }
}
