#!/usr/bin/env python
"""
Simple Hello World Python script for testing iython.
"""

def main():
    print("Hello, World from Python!")
    print("This script is running inside the Java Virtual Machine using Jython.")

    # Demonstrate some basic Python features
    numbers = [1, 2, 3, 4, 5]
    squared = [x**2 for x in numbers]

    print("Original numbers: " + str(numbers))
    print("Squared numbers: " + str(squared))

    # Simple function
    def fibonacci(n):
        if n <= 1:
            return n
        return fibonacci(n-1) + fibonacci(n-2)

    print("Fi<PERSON><PERSON>ci sequence (first 10 numbers):")
    fib_sequence = [fibonacci(i) for i in range(10)]
    print(str(fib_sequence))

if __name__ == "__main__":
    main()
