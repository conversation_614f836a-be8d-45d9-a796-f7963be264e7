package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;

/**
 * AST node representing literal values (numbers, strings, booleans, None).
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class LiteralExpression extends Expression {
    
    private final Object value;
    private final LiteralType type;
    
    public enum LiteralType {
        INTEGER, FLOAT, STRING, BOOLEAN, NONE
    }
    
    public LiteralExpression(int line, int column, Object value, LiteralType type) {
        super(line, column);
        this.value = value;
        this.type = type;
    }
    
    public Object getValue() {
        return value;
    }
    
    public LiteralType getType() {
        return type;
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitLiteral(this);
    }
    
    @Override
    public String toString() {
        return "Literal(" + value + ", " + type + ")";
    }
}
