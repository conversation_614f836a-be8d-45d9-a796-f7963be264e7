package au.com.allianceautomation.iython.runtime;

import java.io.PrintStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import au.com.allianceautomation.iython.ast.ASTVisitor;
import au.com.allianceautomation.iython.ast.Program;
import au.com.allianceautomation.iython.ast.expressions.AttributeExpression;
import au.com.allianceautomation.iython.ast.expressions.BinaryOpExpression;
import au.com.allianceautomation.iython.ast.expressions.BoolOpExpression;
import au.com.allianceautomation.iython.ast.expressions.CallExpression;
import au.com.allianceautomation.iython.ast.expressions.CompareExpression;
import au.com.allianceautomation.iython.ast.expressions.ConditionalExpression;
import au.com.allianceautomation.iython.ast.expressions.DictExpression;
import au.com.allianceautomation.iython.ast.expressions.Expression;
import au.com.allianceautomation.iython.ast.expressions.ListExpression;
import au.com.allianceautomation.iython.ast.expressions.LiteralExpression;
import au.com.allianceautomation.iython.ast.expressions.NameExpression;
import au.com.allianceautomation.iython.ast.expressions.SubscriptExpression;
import au.com.allianceautomation.iython.ast.expressions.UnaryOpExpression;
import au.com.allianceautomation.iython.ast.statements.AssertStatement;
import au.com.allianceautomation.iython.ast.statements.AssignmentStatement;
import au.com.allianceautomation.iython.ast.statements.ClassDefStatement;
import au.com.allianceautomation.iython.ast.statements.DeleteStatement;
import au.com.allianceautomation.iython.ast.statements.ExpressionStatement;
import au.com.allianceautomation.iython.ast.statements.ForStatement;
import au.com.allianceautomation.iython.ast.statements.FunctionDefStatement;
import au.com.allianceautomation.iython.ast.statements.GlobalStatement;
import au.com.allianceautomation.iython.ast.statements.IfStatement;
import au.com.allianceautomation.iython.ast.statements.ImportStatement;
import au.com.allianceautomation.iython.ast.statements.PassStatement;
import au.com.allianceautomation.iython.ast.statements.ReturnStatement;
import au.com.allianceautomation.iython.ast.statements.Statement;
import au.com.allianceautomation.iython.ast.statements.WhileStatement;

/**
 * Runtime system for executing Python AST nodes.
 * This class implements the visitor pattern to traverse and execute AST nodes.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonRuntime implements ASTVisitor<Object> {
    
    private static final Logger logger = LoggerFactory.getLogger(PythonRuntime.class);
    
    private Map<String, Object> variables;
    private PrintStream output;
    private StringBuilder outputBuffer;
    
    public PythonRuntime() {
        this.outputBuffer = new StringBuilder();
    }
    
    /**
     * Execute a Python program.
     * 
     * @param program The program to execute
     * @param globalVars Global variables to use
     * @param outputStream Output stream for print statements
     * @return The captured output
     */
    public String execute(Program program, Map<String, Object> globalVars, PrintStream outputStream) {
        this.variables = new HashMap<>(globalVars);
        this.output = outputStream;
        this.outputBuffer = new StringBuilder();
        
        try {
            program.accept(this);
            
            // Update global variables
            globalVars.putAll(variables);
            
            return outputBuffer.toString();
        } catch (Exception e) {
            logger.error("Runtime error during execution", e);
            throw new RuntimeException("Python execution failed", e);
        }
    }
    
    // Program visitor
    @Override
    public Object visitProgram(Program node) {
        for (Statement stmt : node.getStatements()) {
            stmt.accept(this);
        }
        return null;
    }
    
    // Statement visitors
    @Override
    public Object visitExpressionStmt(ExpressionStatement node) {
        return node.getExpression().accept(this);
    }
    
    @Override
    public Object visitAssignment(AssignmentStatement node) {
        Object value = node.getValue().accept(this);
        
        if (node.getTarget() instanceof NameExpression) {
            String varName = ((NameExpression) node.getTarget()).getName();
            variables.put(varName, value);
        }
        
        return value;
    }
    
    @Override
    public Object visitIf(IfStatement node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitWhile(WhileStatement node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitFor(ForStatement node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitFunctionDef(FunctionDefStatement node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitClassDef(ClassDefStatement node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitReturn(ReturnStatement node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitPass(PassStatement node) {
        // Pass statement does nothing
        return null;
    }
    
    @Override
    public Object visitDelete(DeleteStatement node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitImport(ImportStatement node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitGlobal(GlobalStatement node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitAssert(AssertStatement node) {
        // Placeholder implementation
        return null;
    }
    
    // Expression visitors
    @Override
    public Object visitLiteral(LiteralExpression node) {
        return node.getValue();
    }
    
    @Override
    public Object visitName(NameExpression node) {
        String name = node.getName();
        
        // Check for built-in functions
        if ("print".equals(name)) {
            return new PythonBuiltinFunction("print");
        } else if ("str".equals(name)) {
            return new PythonBuiltinFunction("str");
        }
        
        // Look up variable
        Object value = variables.get(name);
        if (value == null) {
            throw new RuntimeException("NameError: name '" + name + "' is not defined");
        }
        
        return value;
    }
    
    @Override
    public Object visitBinaryOp(BinaryOpExpression node) {
        Object left = node.getLeft().accept(this);
        Object right = node.getRight().accept(this);
        
        switch (node.getOperator()) {
            case ADD:
                return performAddition(left, right);
            case SUBTRACT:
                return performSubtraction(left, right);
            case MULTIPLY:
                return performMultiplication(left, right);
            case DIVIDE:
                return performDivision(left, right);
            case EQUAL:
                return performEquals(left, right);
            case NOT_EQUAL:
                return !performEquals(left, right);
            case LESS_THAN:
                return performLessThan(left, right);
            case GREATER_THAN:
                return performGreaterThan(left, right);
            default:
                throw new RuntimeException("Unsupported binary operator: " + node.getOperator());
        }
    }
    
    @Override
    public Object visitUnaryOp(UnaryOpExpression node) {
        Object operand = node.getOperand().accept(this);
        
        switch (node.getOperator()) {
            case PLUS:
                return operand; // Unary plus
            case MINUS:
                if (operand instanceof Number) {
                    if (operand instanceof Integer) {
                        return -(Integer) operand;
                    } else if (operand instanceof Double) {
                        return -(Double) operand;
                    }
                }
                throw new RuntimeException("Unsupported unary minus for type: " + operand.getClass());
            case NOT:
                return !isTruthy(operand);
            default:
                throw new RuntimeException("Unsupported unary operator: " + node.getOperator());
        }
    }
    
    @Override
    public Object visitCall(CallExpression node) {
        Object function = node.getFunction().accept(this);
        
        if (function instanceof PythonBuiltinFunction) {
            PythonBuiltinFunction builtin = (PythonBuiltinFunction) function;
            
            if ("print".equals(builtin.getName())) {
                // Handle print function
                StringBuilder printOutput = new StringBuilder();
                for (int i = 0; i < node.getArguments().size(); i++) {
                    if (i > 0) printOutput.append(" ");
                    Object arg = node.getArguments().get(i).accept(this);
                    printOutput.append(pythonStr(arg));
                }
                printOutput.append("\n");

                String output = printOutput.toString();
                outputBuffer.append(output);
                if (this.output != null) {
                    this.output.print(output);
                }

                return null; // print returns None
            } else if ("str".equals(builtin.getName())) {
                // Handle str function
                if (node.getArguments().size() != 1) {
                    throw new RuntimeException("str() takes exactly one argument");
                }
                Object arg = node.getArguments().get(0).accept(this);
                return pythonStr(arg);
            }
        }
        
        throw new RuntimeException("Object is not callable: " + function);
    }
    
    @Override
    public Object visitAttribute(AttributeExpression node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitSubscript(SubscriptExpression node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitList(ListExpression node) {
        List<Object> elements = new ArrayList<>();
        for (Expression element : node.getElements()) {
            elements.add(element.accept(this));
        }
        return elements;
    }
    
    @Override
    public Object visitDict(DictExpression node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitCompare(CompareExpression node) {
        // Placeholder implementation
        return null;
    }
    
    @Override
    public Object visitBoolOp(BoolOpExpression node) {
        // Placeholder implementation
        return null;
    }

    @Override
    public Object visitConditional(ConditionalExpression node) {
        Object test = node.getTest().accept(this);
        if (isTruthy(test)) {
            return node.getBody().accept(this);
        } else {
            return node.getOrelse().accept(this);
        }
    }
    
    // Helper methods
    private Object performAddition(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            if (left instanceof Integer && right instanceof Integer) {
                return (Integer) left + (Integer) right;
            } else {
                return ((Number) left).doubleValue() + ((Number) right).doubleValue();
            }
        } else if (left instanceof String || right instanceof String) {
            return pythonStr(left) + pythonStr(right);
        }
        throw new RuntimeException("Unsupported operand types for +: " + left.getClass() + " and " + right.getClass());
    }
    
    private Object performSubtraction(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            if (left instanceof Integer && right instanceof Integer) {
                return (Integer) left - (Integer) right;
            } else {
                return ((Number) left).doubleValue() - ((Number) right).doubleValue();
            }
        }
        throw new RuntimeException("Unsupported operand types for -: " + left.getClass() + " and " + right.getClass());
    }
    
    private Object performMultiplication(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            if (left instanceof Integer && right instanceof Integer) {
                return (Integer) left * (Integer) right;
            } else {
                return ((Number) left).doubleValue() * ((Number) right).doubleValue();
            }
        }
        throw new RuntimeException("Unsupported operand types for *: " + left.getClass() + " and " + right.getClass());
    }
    
    private Object performDivision(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            return ((Number) left).doubleValue() / ((Number) right).doubleValue();
        }
        throw new RuntimeException("Unsupported operand types for /: " + left.getClass() + " and " + right.getClass());
    }
    
    private boolean performEquals(Object left, Object right) {
        if (left == null && right == null) return true;
        if (left == null || right == null) return false;
        return left.equals(right);
    }
    
    private boolean performLessThan(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            return ((Number) left).doubleValue() < ((Number) right).doubleValue();
        }
        throw new RuntimeException("Unsupported operand types for <: " + left.getClass() + " and " + right.getClass());
    }
    
    private boolean performGreaterThan(Object left, Object right) {
        if (left instanceof Number && right instanceof Number) {
            return ((Number) left).doubleValue() > ((Number) right).doubleValue();
        }
        throw new RuntimeException("Unsupported operand types for >: " + left.getClass() + " and " + right.getClass());
    }
    
    private boolean isTruthy(Object obj) {
        if (obj == null) return false;
        if (obj instanceof Boolean) return (Boolean) obj;
        if (obj instanceof Number) return ((Number) obj).doubleValue() != 0.0;
        if (obj instanceof String) return !((String) obj).isEmpty();
        return true; // Most objects are truthy
    }
    
    private String pythonStr(Object obj) {
        if (obj == null) return "None";
        if (obj instanceof String) return (String) obj;
        return obj.toString();
    }
    
    /**
     * Represents a built-in Python function.
     */
    private static class PythonBuiltinFunction {
        private final String name;
        
        public PythonBuiltinFunction(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
        
        @Override
        public String toString() {
            return "<built-in function " + name + ">";
        }
    }
}
