package au.com.allianceautomation.iython;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Main entry point for the iython application.
 * This class provides the main method to run Python code within the JVM.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class IythonMain {
    
    private static final Logger logger = LoggerFactory.getLogger(IythonMain.class);
    
    public static void main(String[] args) {
        logger.info("Starting iython - Python execution in JVM");
        
        try {
            PythonExecutor executor = new PythonExecutor();
            
            if (args.length == 0) {
                // Run demo if no arguments provided
                runDemo(executor);
            } else if (args.length == 1) {
                // Execute Python file
                String pythonFile = args[0];
                logger.info("Executing Python file: {}", pythonFile);
                executor.executeFile(pythonFile);
            } else {
                // Execute Python code directly
                String pythonCode = String.join(" ", args);
                logger.info("Executing Python code: {}", pythonCode);
                executor.executeCode(pythonCode);
            }
            
        } catch (Exception e) {
            logger.error("Error executing Python code", e);
            System.exit(1);
        }
        
        logger.info("iython execution completed");
    }
    
    private static void runDemo(PythonExecutor executor) {
        logger.info("Running iython demo");
        
        String demoCode = "print(\"Hello from Python running in the JVM!\")\n" +
                          "print(\"Python version info:\")\n" +
                          "import sys\n" +
                          "print(\"Python version: \" + str(sys.version))\n" +
                          "print(\"Platform: \" + str(sys.platform))\n" +
                          "\n" +
                          "# Simple calculation\n" +
                          "result = sum(range(1, 11))\n" +
                          "print(\"Sum of numbers 1-10: \" + str(result))\n" +
                          "\n" +
                          "# List comprehension\n" +
                          "squares = [x**2 for x in range(1, 6)]\n" +
                          "print(\"Squares of 1-5: \" + str(squares))";
        
        try {
            executor.executeCode(demoCode);
        } catch (Exception e) {
            logger.error("Error running demo", e);
        }
    }
}
