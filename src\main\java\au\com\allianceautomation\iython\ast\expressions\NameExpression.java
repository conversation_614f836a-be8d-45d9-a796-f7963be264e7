package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;

/**
 * AST node representing variable names and identifiers.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class NameExpression extends Expression {
    
    private final String name;
    
    public NameExpression(int line, int column, String name) {
        super(line, column);
        this.name = name;
    }
    
    public String getName() {
        return name;
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitName(this);
    }
    
    @Override
    public String toString() {
        return "Name(" + name + ")";
    }
}
