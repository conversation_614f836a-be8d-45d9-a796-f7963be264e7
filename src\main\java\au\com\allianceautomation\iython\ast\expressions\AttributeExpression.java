package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;

public class AttributeExpression extends Expression {
    private final Expression value;
    private final String attr;
    
    public AttributeExpression(int line, int column, Expression value, String attr) {
        super(line, column);
        this.value = value;
        this.attr = attr;
    }
    
    public Expression getValue() { return value; }
    public String getAttr() { return attr; }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitAttribute(this);
    }
}
