package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;
import java.util.List;

/**
 * AST node representing function calls.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class CallExpression extends Expression {
    
    private final Expression function;
    private final List<Expression> arguments;
    
    public CallExpression(int line, int column, Expression function, List<Expression> arguments) {
        super(line, column);
        this.function = function;
        this.arguments = arguments;
    }
    
    public Expression getFunction() {
        return function;
    }
    
    public List<Expression> getArguments() {
        return arguments;
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitCall(this);
    }
    
    @Override
    public String toString() {
        return "Call(" + function + ", " + arguments + ")";
    }
}
