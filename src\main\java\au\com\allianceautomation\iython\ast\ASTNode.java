package au.com.allianceautomation.iython.ast;

/**
 * Base class for all AST nodes in the Python interpreter.
 * Provides common functionality for position tracking and visitor pattern support.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public abstract class ASTNode {
    
    private final int line;
    private final int column;
    
    public ASTNode(int line, int column) {
        this.line = line;
        this.column = column;
    }
    
    public int getLine() {
        return line;
    }
    
    public int getColumn() {
        return column;
    }
    
    /**
     * Accept method for the visitor pattern.
     * Each concrete AST node should implement this to call the appropriate visit method.
     * 
     * @param visitor The visitor to accept
     * @param <T> The return type of the visitor
     * @return The result of the visitor operation
     */
    public abstract <T> T accept(ASTVisitor<T> visitor);
    
    @Override
    public String toString() {
        return getClass().getSimpleName() + " at " + line + ":" + column;
    }
}
