package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;

/**
 * AST node representing unary operations (e.g., +x, -x, ~x, not x).
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class UnaryOpExpression extends Expression {
    
    private final UnaryOperator operator;
    private final Expression operand;
    
    public enum UnaryOperator {
        PLUS, MINUS, NOT, INVERT
    }
    
    public UnaryOpExpression(int line, int column, UnaryOperator operator, Expression operand) {
        super(line, column);
        this.operator = operator;
        this.operand = operand;
    }
    
    public UnaryOperator getOperator() {
        return operator;
    }
    
    public Expression getOperand() {
        return operand;
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitUnaryOp(this);
    }
    
    @Override
    public String toString() {
        return "UnaryOp(" + operator + " " + operand + ")";
    }
}
