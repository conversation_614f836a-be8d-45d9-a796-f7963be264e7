package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;

/**
 * AST node representing conditional expressions (ternary operator).
 * Represents expressions like: value_if_true if condition else value_if_false
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ConditionalExpression extends Expression {
    
    private final Expression test;
    private final Expression body;
    private final Expression orelse;
    
    public ConditionalExpression(int line, int column, Expression test, Expression body, Expression orelse) {
        super(line, column);
        this.test = test;
        this.body = body;
        this.orelse = orelse;
    }
    
    public Expression getTest() {
        return test;
    }
    
    public Expression getBody() {
        return body;
    }
    
    public Expression getOrelse() {
        return orelse;
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitConditional(this);
    }
    
    @Override
    public String toString() {
        return "Conditional(" + body + " if " + test + " else " + orelse + ")";
    }
}
