<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>au.com.allianceautomation.iython</groupId>
    <artifactId>iython</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>iython</name>
    <description>Java project for running Python 3 code within the JVM</description>
    <url>https://github.com/allianceautomation/iython</url>

    <organization>
        <name>Alliance Automation Australia</name>
        <url>https://allianceautomation.com.au</url>
    </organization>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <antlr.version>4.13.1</antlr.version>
        <bytebuddy.version>1.14.9</bytebuddy.version>
        <junit.version>5.10.0</junit.version>
        <slf4j.version>2.0.9</slf4j.version>
        <logback.version>1.4.11</logback.version>
    </properties>

    <dependencies>
        <!-- ANTLR4 for Python grammar parsing -->
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
            <version>${antlr.version}</version>
        </dependency>

        <!-- Byte-Buddy for runtime bytecode generation -->
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>${bytebuddy.version}</version>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>${bytebuddy.version}</version>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- ANTLR4 plugin for generating parser classes -->
            <plugin>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-maven-plugin</artifactId>
                <version>${antlr.version}</version>
                <configuration>
                    <sourceDirectory>src/main/antlr4</sourceDirectory>
                    <outputDirectory>target/generated-sources/antlr4</outputDirectory>
                    <visitor>true</visitor>
                    <listener>true</listener>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>antlr4</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <argLine>
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/java.io=ALL-UNNAMED
                        --add-opens java.base/java.net=ALL-UNNAMED
                        --add-opens java.base/java.nio=ALL-UNNAMED
                        --add-opens java.base/java.security=ALL-UNNAMED
                        --add-opens java.base/java.text=ALL-UNNAMED
                        --add-opens java.base/java.time=ALL-UNNAMED
                        --add-opens java.base/sun.nio.ch=ALL-UNNAMED
                        --add-opens java.base/sun.security.util=ALL-UNNAMED
                    </argLine>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>3.1.2</version>
            </plugin>

            <!-- Plugin to create executable JAR -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.5.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>au.com.allianceautomation.iython.IythonMain</mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Plugin for running the application -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>au.com.allianceautomation.iython.IythonMain</mainClass>
                    <options>
                        <option>--add-opens</option>
                        <option>java.base/java.lang=ALL-UNNAMED</option>
                        <option>--add-opens</option>
                        <option>java.base/java.util=ALL-UNNAMED</option>
                        <option>--add-opens</option>
                        <option>java.base/java.io=ALL-UNNAMED</option>
                        <option>--add-opens</option>
                        <option>java.base/java.net=ALL-UNNAMED</option>
                        <option>--add-opens</option>
                        <option>java.base/java.nio=ALL-UNNAMED</option>
                        <option>--add-opens</option>
                        <option>java.base/java.security=ALL-UNNAMED</option>
                        <option>--add-opens</option>
                        <option>java.base/java.text=ALL-UNNAMED</option>
                        <option>--add-opens</option>
                        <option>java.base/java.time=ALL-UNNAMED</option>
                        <option>--add-opens</option>
                        <option>java.base/sun.nio.ch=ALL-UNNAMED</option>
                        <option>--add-opens</option>
                        <option>java.base/sun.security.util=ALL-UNNAMED</option>
                    </options>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <licenses>
        <license>
            <name>MIT License</name>
            <url>https://opensource.org/licenses/MIT</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>Alliance Automation Australia</name>
            <email><EMAIL></email>
            <organization>Alliance Automation Australia</organization>
            <organizationUrl>https://allianceautomation.com.au</organizationUrl>
        </developer>
    </developers>

    <scm>
        <connection>scm:git:git://github.com/allianceautomation/iython.git</connection>
        <developerConnection>scm:git:ssh://github.com:allianceautomation/iython.git</developerConnection>
        <url>http://github.com/allianceautomation/iython/tree/main</url>
    </scm>
</project>
